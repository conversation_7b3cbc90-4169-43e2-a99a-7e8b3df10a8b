import React, { useEffect, useState } from 'react';
import {
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  SlideInUp,
  SlideOutDown,
  useAnimatedProps,
  useSharedValue,
  withSpring,
  withTiming,
  ZoomIn,
  ZoomOut
} from 'react-native-reanimated';
import {
  moderateScale,
  moderateVerticalScale,
  scale,
  verticalScale,
} from 'react-native-size-matters';
import Svg, { G, Image as SvgImage } from 'react-native-svg';
import { SvgIcons } from '../../../assets/svg/index.tsx';
import { RolePlayCard } from '../../../assets/svg/roleplay/RolePlayCard.tsx';
import { RecordButtonWithRipple } from '../../../common/animation/RippleRecordButton.tsx';
import Button from '../../components/Button.tsx';
import TextApp from '../../components/TextApp/index.tsx';
import { goBack } from '../../navigation/NavigationServices.ts';
import { Theme } from '../../themes/index.ts';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale.ts';
const AnimatedSvgImage = Animated.createAnimatedComponent(SvgImage);

interface RolePlayTopic {
  id: string;
  title: string;
  image: any;
  question: string;
  mission: string;
  reward: string;
  missions: Mission[];
}

interface Mission {
  id: string;
  text: string;
  completed: boolean;
}

const rolePlayData = [
  {
    title: 'First day of schook',
    image: Theme.images.rolePlayCardCharactor1,
    question: 'What do you like to eat?',
    mission: `🎯 Mission: Tell Cheppy your 3 \nfavorite foods and ask him!`,
    reward: '20',
    color: '#6D4800',
  },
  {
    title: 'First day of schook',
    image: Theme.images.rolePlayCardCharactor2,
    question: 'What is your favorite color?',
    mission: '🎯 Mission: Share your top 3 \nfavorite colors with Cheppy!',
    reward: '15',
    color: '#932D1C',
  },
  {
    title: 'First day of schook',
    image: Theme.images.rolePlayCardCharactor3,
    question: 'Let’s talk about pets!',
    mission:
      '🎯 Mission: Tell Cheppy what pet you \nhave or want and what it can do.',
    reward: '25',
    color: '#0F6866',
  },
];

const missions = [
  {text: 'Say hello', completed: false},
  {text: 'Tell your name', completed: true},
  {text: 'Ask 1 question', completed: true},
];

const RolePlay: React.FC = () => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalContent, setModalContent] = useState<{
    title: string;
    image: any;
    question: string;
    mission: string;
    reward: string;
    color: string;
  } | null>(null);
  const [showMission, setShowMission] = useState<boolean>(false);
  const [quitModal, setQuitModal] = useState<boolean>(false);
  const [finishModal, setFinishModal] = useState<boolean>(false);

  const trasnY = useSharedValue(-300);
  const opacity = useSharedValue(0);

  useEffect(() => {
    setTimeout(() => {
      trasnY.value = withSpring(0);
      opacity.value = withTiming(1, {duration: 300});
    }, 200);
  }, []);

  useEffect(() => {
    opacity.value = withTiming(0, {duration: 150});
    opacity.value = withTiming(1, {duration: 200});
  }, [modalContent?.image]);

  const animatedProps = useAnimatedProps(() => ({
    y: trasnY.value,
  }));

  const animatedStyle = useAnimatedProps(() => ({
    opacity: opacity.value,
  }));

  const handleChooseCard = (index: number) => {
    setModalContent(rolePlayData[index]);
    setTimeout(() => {
      setModalVisible(true);
    }, 200);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  const closeQuitModal = () => {
    setQuitModal(false);
  };

  const handleQuit = () => {
    setQuitModal(false);
    setShowMission(false);
    setTimeout(() => {
      goBack();
    }, 300);
  };

  const onBackPress = () => {
    if (showMission) {
      setQuitModal(true);
    } else {
      goBack();
    }
  };

  const handleStartNow = () => {
    handleCloseModal();
    setTimeout(() => {
      setShowMission(true);
      trasnY.value = withSpring(-300, {
        damping: 15,
        stiffness: 120,
      });
    }, 300);
  };

  const handleCompleteMission = () => {
    setTimeout(() => {
      setFinishModal(true);
    }, 3000);
  };

  const contentModal = () => {
    return (
      <ImageBackground
        style={{
          width: 295.39,
          height: 465.79,
        }}
        source={Theme.images.bgConfirmChooseCard}
        resizeMode="stretch">
        <TouchableOpacity style={styles.closeButton} onPress={handleCloseModal}>
          <FastImage
            source={require('../../../assets/svg/roleplay/close.png')}
            style={{width: 45, height: 45}}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <TextApp
          text={modalContent?.title}
          preset="display_xs_semibold"
          textColor="#7C3603"
          style={{
            textAlign: 'center',
            lineHeight: 32,
            marginTop: moderateVerticalScale(30),
          }}
        />
        <View style={styles.characterRow}>
          <View style={styles.characterBox}>
            <View style={styles.avatarCircle}>
              <FastImage
                source={Theme.images.rolePlayCardCharactor2}
                style={styles.avatarImage}
                resizeMode="contain"
              />
            </View>
            <TextApp
              preset="text_xs_semibold"
              text={'You are:'}
              style={styles.characterName}
            />
            <TextApp
              preset="text_xs_regular"
              text={'A New Student'}
              style={styles.characterRole}
            />
          </View>

          <View style={styles.characterBox}>
            <View style={[styles.avatarCircle, {backgroundColor: '#3A600D'}]}>
              <FastImage
                source={Theme.images.rolePlayCardCharactor3}
                style={styles.avatarImage}
                resizeMode="contain"
              />
            </View>
            <TextApp
              preset="text_xs_semibold"
              text={'Cheppy:'}
              style={styles.characterName}
            />
            <TextApp
              preset="text_xs_regular"
              text={'A New Student'}
              style={styles.characterRole}
            />
          </View>
        </View>
        <TextApp
          preset="text_xs_regular"
          text={`Mission: Say Hello, tell your name\nand ask 1 question to a new friend`}
          style={styles.mission}
          textColor="#181D27"
        />
        <View style={styles.rewardBox}>
          <TextApp
            text={`Reward: ${modalContent?.reward}`}
            style={styles.rewardText}
          />
          <FastImage source={Theme.icons.gem} style={styles.pearlIcon} />
        </View>
        <TouchableOpacity onPress={handleStartNow}>
          <FastImage
            source={require('../../../assets/svg/roleplay/start-now.png')}
            style={{width: 267.96, height: 87.22, alignSelf: 'center'}}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </ImageBackground>
    );
  };

  const contentQuitModal = () => {
    return (
      <View
        style={{
          backgroundColor: '#fff',
          paddingHorizontal: 20,
          paddingVertical: 20,
          borderRadius: 6,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <View
          style={{
            position: 'absolute',
            top: -100,
          }}>
          <SvgIcons.Quit />
        </View>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={closeQuitModal}
          hitSlop={HIT_SLOP}>
          <TextApp text="×" preset="text_lg_bold" />
        </TouchableOpacity>
        <View style={{height: 100}} />
        <TextApp
          text="You want to quit?"
          preset="text_xl_bold"
          style={{textAlign: 'center'}}
        />
        <TextApp
          text={'You will lost your process. \nIf yes, tap the button below.'}
          preset="text_md_regular"
          style={{textAlign: 'center', marginVertical: 10}}
        />
        <Button
          title="Quit"
          onPress={handleQuit}
          style={{
            backgroundColor: '#F99A3D',
            width: '80%',
            marginTop: 10,
          }}
        />
      </View>
    );
  };

  const renderFinishModal = () => {
    return (
      <View
        style={{
          backgroundColor: '#fff',
          paddingHorizontal: 20,
          paddingVertical: 20,
          borderRadius: 6,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <TextApp
          text="MISSION COMPLETE!"
          preset="text_xl_bold"
          textColor="#F99A3D"
          style={{textAlign: 'center'}}
        />
        <Button
          title="Continue"
          onPress={() => {
            setFinishModal(false);
            setShowMission(false);
            goBack();
          }}
          style={{
            backgroundColor: '#F99A3D',
            width: '80%',
            marginTop: 20,
          }}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={onBackPress}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
      </View>
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 375 812"
        preserveAspectRatio="none"
        fill="none">
        <G id="">
          <G id="">
            <SvgImage
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={812}
              preserveAspectRatio="none"
              href={Theme.images.rolePlayBlur}
            />
          </G>
          <G id="1">
            <AnimatedSvgImage
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={248.16}
              preserveAspectRatio="none"
              href={Theme.images.rolePlayName}
              animatedProps={animatedProps}
            />
          </G>
          <G id="background">
            <SvgImage
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={812}
              preserveAspectRatio="xMidYMid slice"
              href={Theme.images.bgRolePlay}
            />
          </G>
          <G id="">
            <AnimatedSvgImage
              x={115.51}
              y={310.3}
              id="image0_367_1053"
              width={143.97}
              height={236.7}
              preserveAspectRatio="none"
              href={
                modalContent?.image ?? Theme.images.rolePlayCharactorDefault
              }
              animatedProps={animatedStyle}
            />
          </G>
        </G>
      </Svg>
      {showMission ? (
        <Animated.View
          entering={ZoomIn}
          exiting={ZoomOut}
          style={styles.missionContainer}>
          <ImageBackground
            source={require('../../../assets/svg/roleplay/mission-roleplay.png')}
            style={{width: 344.22, height: 135.86}}>
            <View style={{position: 'absolute', left: 0, right: 0, top: -80}}>
              <RecordButtonWithRipple />
            </View>
            <TextApp
              preset="text_md_semibold"
              text={'MISSIONS NEED TO COMPLETED'}
              textColor="#7C3603"
              style={{lineHeight: 24, textAlign: 'center', marginTop: 28}}
            />
            <View style={styles.missionWrapper}>
              {missions.map((mission, index) => (
                <View key={index} style={styles.missionItem}>
                  <View
                    style={{
                      width: 16,
                      height: 16,
                      borderRadius: 20,
                      borderWidth: 0.5,
                      borderColor: '#D5D7DA',
                    }}
                  />
                  <TextApp
                    preset="text_sm_regular"
                    text={mission.text}
                    style={styles.missionText}
                    textColor="#181D27"
                  />
                </View>
              ))}
            </View>
          </ImageBackground>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginHorizontal: scale(40),
              marginTop: moderateVerticalScale(5),
            }}>
            <TouchableOpacity
              style={{
                backgroundColor: '#FFCA11',
                paddingHorizontal: 20,
                paddingVertical: 5,
                borderRadius: 10,
              }}>
              <TextApp
                preset="text_md_bold"
                text={'CC'}
                textColor="#BF8400"
                style={{lineHeight: 20}}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                backgroundColor: '#FFCA11',
                paddingHorizontal: 20,
                paddingVertical: 4,
                borderRadius: 10,
              }}>
              <SvgIcons.Idea />
            </TouchableOpacity>
          </View>
        </Animated.View>
      ) : (
        <View style={styles.cardContainer}>
          {rolePlayData.map((item, index) => (
            <RolePlayCard
              key={index}
              color={item?.color}
              href={item?.image}
              onPressCard={handleChooseCard?.bind(null, index)}
            />
          ))}
          <TouchableOpacity
            style={{
              width: 80,
              height: 100,
            }}>
            <FastImage
              source={Theme.images.rolePlayCardCharactor4}
              style={{width: '100%', height: '100%'}}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      )}
      {modalVisible && (
        <Animated.View
          entering={SlideInUp}
          exiting={SlideOutDown}
          style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            onPress={handleCloseModal}
            activeOpacity={1}
          />
          <View style={styles.modalContent}>{contentModal()}</View>
        </Animated.View>
      )}

      {quitModal && (
        <Animated.View
          entering={ZoomIn}
          exiting={ZoomOut}
          style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            onPress={closeQuitModal}
            activeOpacity={1}
          />
          <View style={styles.modalContent}>{contentQuitModal()}</View>
        </Animated.View>
      )}

      {finishModal && (
        <Animated.View
          entering={ZoomIn}
          exiting={ZoomOut}
          style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            onPress={() => setFinishModal(false)}
            activeOpacity={1}
          />
          <View style={styles.modalContent}>{renderFinishModal()}</View>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: heightScreen,
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  cardContainer: {
    flexDirection: 'row',
    gap: scale(13),
    justifyContent: 'center',
    position: 'absolute',
    alignSelf: 'center',
    bottom: (85 / 813) * heightScreen,
  },
  closeButton: {
    position: 'absolute',
    right: -5,
    top: -5,
    width: 25,
    height: 25,
    backgroundColor: '#D9D9D9',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  missionContainer: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: (60 / 813) * heightScreen,
  },
  missionTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    color: '#FFF',
    textAlign: 'center',
  },
  missionItem: {
    width: '45%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  missionStatusIncomplete: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(24),
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#FF7E00',
    marginRight: scale(10),
  },
  missionStatusComplete: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(24),
    backgroundColor: '#00FF00',
    marginRight: scale(10),
  },
  missionText: {
    lineHeight: 18,
    marginLeft: 5,
  },

  characterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 30,
    marginBottom: 20,
    marginTop: 16,
  },
  characterBox: {
    alignItems: 'center',
    flex: 1,
  },
  avatarCircle: {
    backgroundColor: '#843F00',
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  characterName: {
    lineHeight: 18,
  },
  characterRole: {
    lineHeight: 18,
  },
  mission: {
    textAlign: 'center',
    marginBottom: 20,
  },
  rewardBox: {
    width: 160,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF3CA',
    paddingVertical: 10,
    borderRadius: 8,
  },
  rewardText: {
    marginRight: 8,
    lineHeight: 18,
  },
  pearlIcon: {
    width: 21,
    height: 21,
    resizeMode: 'contain',
  },
  missionWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
  },
  // Modal styles to fix SVG layout conflicts
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    maxWidth: '90%',
    maxHeight: '80%',
  },
});

export default RolePlay;
