import React, {useState} from 'react';
import {
  ImageBackground,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import Animated, {ZoomIn, ZoomOut} from 'react-native-reanimated';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg/index.tsx';
import {RecordButtonWithRipple} from '../../../common/animation/RippleRecordButton.tsx';
import Button from '../../components/Button.tsx';
import CustomModal from '../../components/CustomModal.tsx';
import Header from '../../components/Header.tsx';
import {goBack} from '../../navigation/NavigationServices.ts';
import {Theme} from '../../themes/index.ts';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  heightScreen,
  HIT_SLOP,
  initTop,
  isAndroid,
  widthScreen,
} from '../../utils/Scale.ts';
import Svg, {G, Image} from 'react-native-svg';

// const modalData = [
//   {
//     title: 'Favorite food',
//     image: Theme.images.favoriteFood,
//     question: 'What do you like to eat?',
//     mission: `🎯 Mission: Tell Cheppy your 3 \nfavorite foods and ask him!`,
//     reward: "You'll get 20",
//   },
//   {
//     title: 'Favorite color',
//     image: Theme.images.yourPet,
//     question: 'What is your favorite color?',
//     mission: '🎯 Mission: Share your top 3 \nfavorite colors with Cheppy!',
//     reward: "You'll get 15",
//   },
//   {
//     title: 'Your pet',
//     image: Theme.images.yourPet,
//     question: 'Let’s talk about pets!',
//     mission:
//       '🎯 Mission: Tell Cheppy what pet you \nhave or want and what it can do.',
//     reward: "You'll get 25",
//   },
//   {
//     title: 'Favorite place',
//     image: Theme.images.yourPet,
//     question: 'Where do you like to go?',
//     mission: '🎯 Mission: Share your top 3 \nfavorite places with Cheppy!',
//     reward: "You'll get 30",
//   },
// ];

const RolePlay: React.FC = () => {
  // const [modalVisible, setModalVisible] = useState<boolean>(false);
  // const [modalContent, setModalContent] = useState<{
  //   title: string;
  //   image: any;
  //   question: string;
  //   mission: string;
  //   reward: string;
  // } | null>(null);
  // const [showMission, setShowMission] = useState<boolean>(false);
  // const [quitModal, setQuitModal] = useState<boolean>(false);
  // const [finishModal, setFinishModal] = useState<boolean>(false);

  // const handleChooseCard = (index: number) => {
  //   setModalContent(modalData[index]);
  //   setModalVisible(true);
  // };

  // const handleCloseModal = () => {
  //   setModalVisible(false);
  //   setTimeout(() => {
  //     setModalContent(null);
  //   }, 300);
  // };

  // const closeQuitModal = () => {
  //   setQuitModal(false);
  // };

  // const handleQuit = () => {
  //   setQuitModal(false);
  //   setTimeout(() => {
  //     goBack();
  //   }, 300);
  // };

  // const handleStartNow = () => {
  //   handleCloseModal();
  //   setShowMission(true);
  // };

  // const onBackPress = () => {
  //   if (showMission) {
  //     setQuitModal(true);
  //   } else {
  //     goBack();
  //   }
  // };

  // const contentModal = () => {
  //   return (
  //     <View style={styles.outerContainer}>
  //       <TouchableOpacity style={styles.closeButton} onPress={handleCloseModal}>
  //         <Text>X</Text>
  //       </TouchableOpacity>
  //       <View style={styles.innerContainer}>
  //         <Text style={styles.titleText}>{modalContent?.title}</Text>
  //         <Image
  //           source={modalContent?.image}
  //           style={styles.image}
  //           resizeMode="cover"
  //         />
  //         <Text style={styles.questionText}>{modalContent?.question}</Text>
  //         <Text style={styles.missionTextModal}>{modalContent?.mission}</Text>
  //         <View style={styles.rewardContainer}>
  //           <Text style={styles.rewardText}>{modalContent?.reward}</Text>
  //         </View>
  //         <Button
  //           title="Start Now"
  //           onPress={handleStartNow}
  //           style={styles.startButton}
  //         />
  //       </View>
  //     </View>
  //   );
  // };

  // const contentQuitModal = () => {
  //   return (
  //     <View
  //       style={{
  //         backgroundColor: '#fff',
  //         paddingHorizontal: 20,
  //         paddingVertical: 20,
  //         borderRadius: 6,
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //       }}>
  //       <View
  //         style={{
  //           position: 'absolute',
  //           top: -100,
  //         }}>
  //         <SvgIcons.Quit />
  //       </View>
  //       <TouchableOpacity style={styles.closeButton} onPress={closeQuitModal}>
  //         <Text>X</Text>
  //       </TouchableOpacity>
  //       <View style={{height: 100}} />
  //       <Text style={{fontSize: 20, fontWeight: '700'}}>You want to quit?</Text>
  //       <Text style={{fontSize: 15, fontWeight: '400', textAlign: 'center'}}>
  //         You will lost your process. {'\n'}If yes, tap the button below.
  //       </Text>
  //       <Button title="Quit" onPress={handleQuit} style={styles.startButton} />
  //     </View>
  //   );
  // };

  // const renderFinishModal = () => {
  //   return (
  //     <View
  //       style={{
  //         backgroundColor: '#fff',
  //         paddingHorizontal: 20,
  //         paddingVertical: 20,
  //         borderRadius: 6,
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //       }}>
  //       <Text style={{fontSize: 20, fontWeight: '700', color: '#F99A3Dho'}}>
  //         MISSION COMPLETE!
  //       </Text>
  //     </View>
  //   );
  // };

  return (
    // <ImageBackground
    //   style={styles.container}
    //   source={Theme.images.bgFunTalk}
    //   resizeMode="stretch">
    //   <StatusBar
    //     translucent
    //     backgroundColor="transparent"
    //     barStyle="dark-content"
    //   />
    //   <Header onBackPress={onBackPress} />

    //   {showMission ? (
    //     <Animated.View
    //       entering={ZoomIn}
    //       exiting={ZoomOut}
    //       style={styles.missionContainer}>
    //       <Text style={styles.missionTitle}>MISSIONS NEED TO COMPLETED</Text>
    //       <View style={styles.missionItem}>
    //         <View style={styles.missionStatusIncomplete} />
    //         <Text style={styles.missionText}>Tell 2 favorite foods 1/2</Text>
    //       </View>
    //       <View style={styles.missionItem}>
    //         <View style={styles.missionStatusComplete} />
    //         <Text style={styles.missionText}>Know Cheppy's favorite food</Text>
    //       </View>
    //       <RecordButtonWithRipple />
    //     </Animated.View>
    //   ) : (
    //     <View style={styles.cardContainer}>
    //       {['Card 1', 'Card 2', 'Card 3', 'Card 4'].map((label, index) => (
    //         <TouchableOpacity
    //           key={index}
    //           style={styles.cardButton}
    //           onPress={() => handleChooseCard(index)}>
    //           <Text style={styles.cardButtonText}>{label}</Text>
    //         </TouchableOpacity>
    //       ))}
    //     </View>
    //   )}

    //   {showMission && (
    //     <View style={styles.bottomContainer}>
    //       <TouchableOpacity style={styles.bottomButton}>
    //         <Text style={styles.bottomButtonText}>CC</Text>
    //       </TouchableOpacity>
    //       <TouchableOpacity style={styles.bottomButton}>
    //         <SvgIcons.Idea />
    //       </TouchableOpacity>
    //     </View>
    //   )}

    //   <CustomModal
    //     visible={modalVisible}
    //     animationIn={'slideInDown'}
    //     animationOut={'slideOutUp'}
    //     animationOutTiming={1000}
    //     animationInTiming={1000}
    //     children={contentModal()}
    //   />
    //   <CustomModal
    //     visible={quitModal}
    //     animationIn={'fadeIn'}
    //     animationOut={'fadeOut'}
    //     animationOutTiming={1000}
    //     animationInTiming={1000}
    //     children={contentQuitModal()}
    //   />
    //   <CustomModal
    //     visible={finishModal}
    //     animationIn={'fadeIn'}
    //     animationOut={'fadeOut'}
    //     animationOutTiming={1000}
    //     animationInTiming={1000}
    //     children={renderFinishModal()}
    //   />
    // </ImageBackground>

    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.back}
          onPress={goBack}
          hitSlop={HIT_SLOP}>
          <SvgIcons.ArrowLeft />
        </TouchableOpacity>
      </View>
      <Svg
        width={'100%'}
        height={'100%'}
        viewBox="0 0 375 812"
        preserveAspectRatio="none"
        fill="none">
        <G id="">
          <G id="">
            <Image
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={812}
              preserveAspectRatio="none"
              href={Theme.images.rolePlayBlur}
            />
          </G>
          <G id="">
            <Image
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={248.16}
              preserveAspectRatio="none"
              href={Theme.images.rolePlayName}
            />
          </G>
          <G id="background">
            <Image
              x={0}
              y={0}
              id="image0_367_1053"
              width={375}
              height={812}
              preserveAspectRatio="xMidYMid slice"
              href={Theme.images.bgRolePlay}
            />
          </G>
        </G>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: widthScreen,
    height: heightScreen,
  },
  header: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    top: isAndroid ? verticalScale(30) : initTop,
    zIndex: 1,
  },
  back: {
    width: 36,
    height: 36,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Theme.radius.radius_md,
    borderWidth: 1,
    borderColor: '#D5D7DA',
  },
  // outerContainer: {
  //   backgroundColor: '#FFEECD',
  //   borderRadius: 19,
  //   padding: 9,
  // },
  // closeButton: {
  //   position: 'absolute',
  //   right: -10,
  //   top: -10,
  //   width: 25,
  //   height: 25,
  //   backgroundColor: '#D9D9D9',
  //   borderRadius: 40,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  // },
  // innerContainer: {
  //   backgroundColor: '#fff',
  //   borderWidth: 3,
  //   borderColor: '#F99A3D',
  //   borderRadius: 19,
  //   paddingHorizontal: 15,
  //   paddingVertical: 20,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  // },
  // titleText: {
  //   fontSize: 20,
  //   fontWeight: '700',
  //   color: '#FF7E00',
  // },
  // image: {
  //   width: 144,
  //   height: 144,
  // },
  // questionText: {
  //   fontSize: 18,
  //   fontWeight: '700',
  // },
  // missionTextModal: {
  //   fontSize: 12,
  //   fontWeight: '400',
  //   fontStyle: 'italic',
  //   lineHeight: 22,
  //   marginVertical: 10,
  //   textAlign: 'center',
  // },
  // rewardContainer: {
  //   backgroundColor: '#D9D9D9',
  //   paddingHorizontal: 20,
  //   paddingVertical: 10,
  //   borderRadius: 7,
  //   marginTop: 5,
  // },
  // rewardText: {
  //   fontSize: 12,
  //   fontWeight: '400',
  //   fontStyle: 'italic',
  // },
  // startButton: {
  //   backgroundColor: '#F99A3D',
  //   width: '80%',
  //   marginTop: 10,
  // },
  // cardContainer: {
  //   flexDirection: 'row',
  //   gap: scale(5),
  //   justifyContent: 'center',
  //   position: 'absolute',
  //   alignSelf: 'center',
  //   bottom: verticalScale(175),
  // },
  // cardButton: {
  //   backgroundColor: '#FFA500',
  //   width: scale(60),
  //   height: verticalScale(100),
  //   borderRadius: moderateScale(12),
  //   margin: scale(5),
  //   justifyContent: 'center',
  //   alignItems: 'center',
  // },
  // cardButtonText: {
  //   fontSize: moderateScale(14),
  //   color: 'white',
  //   fontWeight: 'bold',
  // },
  // missionContainer: {
  //   position: 'absolute',
  //   alignSelf: 'center',
  //   bottom: verticalScale(160),
  // },
  // missionTitle: {
  //   fontSize: moderateScale(20),
  //   fontWeight: 'bold',
  //   color: '#FFF',
  //   textAlign: 'center',
  // },
  // missionItem: {
  //   flexDirection: 'row',
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   marginTop: verticalScale(10),
  // },
  // missionStatusIncomplete: {
  //   width: scale(24),
  //   height: scale(24),
  //   borderRadius: scale(24),
  //   backgroundColor: '#FFFFFF',
  //   borderWidth: 2,
  //   borderColor: '#FF7E00',
  //   marginRight: scale(10),
  // },
  // missionStatusComplete: {
  //   width: scale(24),
  //   height: scale(24),
  //   borderRadius: scale(24),
  //   backgroundColor: '#00FF00',
  //   marginRight: scale(10),
  // },
  // missionText: {
  //   fontSize: 16,
  //   fontWeight: '700',
  //   color: '#fff',
  // },
  // bottomContainer: {
  //   flex: 1,
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  //   alignItems: 'center',
  //   position: 'absolute',
  //   bottom: verticalScale(30),
  //   left: 35,
  //   right: 35,
  //   height: 35,
  // },
  // bottomButton: {
  //   width: 56,
  //   height: 37,
  //   borderRadius: 7,
  //   borderWidth: 2,
  //   borderColor: '#fff',
  //   justifyContent: 'center',
  //   alignItems: 'center',
  // },
  // bottomButtonText: {
  //   fontSize: 20,
  //   fontWeight: '700',
  //   color: '#fff',
  // },
});

export default RolePlay;
