import React, {useEffect, useMemo, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Animated, {
  cancelAnimation,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import {Theme} from '../../src/themes';

const barHeights = [
  10, 20, 40, 20, 10, 10, 20, 40, 20, 10, 10, 20, 40, 20, 10, 10, 20, 40, 20,
  10,
];

export const WaveBar = ({
  index,
  isRecording,
}: {
  index: number;
  isRecording: boolean;
}) => {
  const scaleY = useSharedValue(1);

  useEffect(() => {
    if (isRecording) {
      scaleY.value = withRepeat(
        withTiming(2, {
          duration: 400,
        }),
        -1,
        true,
      );
    } else {
      cancelAnimation(scaleY);
      scaleY.value = 1;
    }
  }, [isRecording]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scaleY: scaleY.value}],
  }));

  return (
    <Animated.View
      style={[styles.bar, {height: barHeights[index]}, animatedStyle]}
    />
  );
};

export const RecordButtonWithRipple = ({
  callBackRecording,
}: {
  callBackRecording?: () => void;
}) => {
  const [isRecording, setIsRecording] = useState<boolean>(false);

  const toggleRecording = () => {
    setIsRecording(prev => !prev);
    callBackRecording?.();
  };

  const leftBars = useMemo(() => {
    if (!isRecording) return null;
    return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => (
      <WaveBar key={`left-${i}`} index={i} isRecording={true} />
    ));
  }, [isRecording]);

  const rightBars = useMemo(() => {
    if (!isRecording) return null;
    return [10, 11, 12, 13, 14, 15, 16, 17, 18, 19].map(i => (
      <WaveBar key={`right-${i}`} index={i} isRecording={true} />
    ));
  }, [isRecording]);

  return (
    <View style={styles.container}>
      <View style={styles.waveWrapper}>
        <View style={styles.waveSide}>{leftBars}</View>
        <TouchableOpacity style={styles.button} onPress={toggleRecording}>
          {/* <.Image
            source={isRecording ? Theme.icons.arrowTop : Theme.icons.audio}
            style={styles.icon}
          /> */}
        </TouchableOpacity>
        <View style={styles.waveSide}>{rightBars}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  waveWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  waveSide: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  button: {
    width: 71,
    height: 71,
    borderRadius: 100,
    backgroundColor: '#F99A3D',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#F99A3D',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
    borderWidth: 5,
    borderColor: '#fff',
  },
  icon: {
    width: 35,
    height: 35,
    tintColor: '#fff',
  },
  bar: {
    width: 6,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
});
